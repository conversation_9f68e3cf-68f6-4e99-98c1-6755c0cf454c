body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

* {
  box-sizing: border-box;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.dashboard {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-top: 20px;
}

.card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease-in-out;
}

.card:hover {
  transform: translateY(-2px);
}

.card h2 {
  margin-top: 0;
  color: #333;
  font-size: 1.5rem;
  font-weight: 600;
}

.status-indicator {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-connected {
  background-color: #10b981;
}

.status-disconnected {
  background-color: #ef4444;
}

.connection-status {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-weight: 500;
}

.song-list {
  max-height: 400px;
  overflow-y: auto;
}

.song-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f3f4f6;
}

.song-item:last-child {
  border-bottom: none;
}

.song-info {
  flex: 1;
}

.song-title {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.song-artist {
  color: #6b7280;
  font-size: 0.875rem;
}

.song-stats {
  text-align: right;
}

.song-plays {
  font-weight: 600;
  color: #059669;
  font-size: 1.125rem;
}

.song-users {
  color: #6b7280;
  font-size: 0.875rem;
}

.alert-list {
  max-height: 400px;
  overflow-y: auto;
}

.alert-item {
  padding: 16px;
  border-radius: 8px;
  background: #fef2f2;
  border-left: 4px solid #ef4444;
}

.alert-user {
  font-weight: 600;
  color: #dc2626;
  margin-bottom: 4px;
}

.alert-details {
  color: #6b7280;
  font-size: 0.875rem;
}

.header {
  text-align: center;
  color: white;
  margin-bottom: 30px;
}

.header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.header p {
  font-size: 1.125rem;
  opacity: 0.9;
  margin: 0;
}

@media (max-width: 768px) {
  .dashboard {
    grid-template-columns: 1fr;
  }
  
  .container {
    padding: 10px;
  }
  
  .header h1 {
    font-size: 2rem;
  }
} 