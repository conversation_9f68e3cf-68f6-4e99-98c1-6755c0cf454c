.App {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.navbar {
  background-color: #1a202c;
  color: white;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.navbar-brand {
  font-size: 1.25rem;
  font-weight: bold;
}

.navbar-links a {
  color: #a0aec0;
  text-decoration: none;
  margin-left: 1.5rem;
  font-size: 0.875rem;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background-color 0.2s, color 0.2s;
}

.navbar-links a:hover {
  background-color: #2d3748;
  color: white;
}

.navbar-links a.active {
  background-color: #667eea;
  color: white;
}

.navbar-external-link {
  background: none;
  border: none;
  color: #718096;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  transition: all 0.2s;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
}

.navbar-external-link:hover {
  background-color: #667eea;
  color: white;
}

main {
  flex-grow: 1;
  overflow-y: auto;
  background-color: #f7fafc;
  padding: 20px;
  min-height: calc(100vh - 64px);
}

.producer-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.producer-actions button {
  background-color: #4299e1;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.25rem;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.producer-actions button:hover {
  background-color: #3182ce;
}

.producer-actions button:disabled {
  background-color: #a0aec0;
  cursor: not-allowed;
}

.log-container {
  background-color: #1a202c;
  color: #e2e8f0;
  font-family: 'Courier New', Courier, monospace;
  padding: 1rem;
  border-radius: 0.25rem;
  height: 400px;
  overflow-y: auto;
  font-size: 0.875rem;
}

.log-message {
  padding: 0.25rem 0;
  border-bottom: 1px solid #2d3748;
}

.log-message:last-child {
  border-bottom: none;
}

.log-error {
  color: #fc8181;
}

/* Dashboard Styles */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.header h1 {
  color: #2d3748;
  margin-bottom: 8px;
  font-size: 2rem;
}

.header p {
  color: #718096;
  font-size: 1rem;
}

.connection-status {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  padding: 8px 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  font-size: 0.875rem;
}

.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-connected {
  background-color: #48bb78;
  animation: pulse 2s infinite;
}

.status-disconnected {
  background-color: #f56565;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.dashboard {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
  align-items: stretch;
}

.dashboard .card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  transition: transform 0.2s, box-shadow 0.2s;
  height: 500px;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.dashboard .card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 15px rgba(0,0,0,0.1);
}

.dashboard .card h2 {
  color: #2d3748;
  margin-bottom: 12px;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 6px;
  flex-shrink: 0;
}

.song-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.song-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 10px;
  background: #f7fafc;
  border-radius: 6px;
  border-left: 3px solid #667eea;
  min-height: 50px;
  box-sizing: border-box;
}

.song-info {
  flex: 1;
}

.song-title {
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 2px;
  font-size: 0.8rem;
}

.song-artist {
  color: #718096;
  font-size: 0.75rem;
}

.song-stats {
  text-align: right;
}

.song-plays {
  font-weight: 600;
  color: #667eea;
  margin-bottom: 1px;
  font-size: 0.8rem;
}

.song-users {
  color: #718096;
  font-size: 0.75rem;
}

.alert-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.alert-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 10px;
  background: #fed7d7;
  border-radius: 6px;
  border-left: 3px solid #f56565;
  flex-shrink: 0;
  min-height: 50px;
  box-sizing: border-box;
}

.alert-info {
  flex: 1;
}

.alert-user {
  font-weight: 600;
  color: #c53030;
  font-size: 0.8rem;
}

.alert-details {
  color: #742a2a;
  font-size: 0.75rem;
}

.alert-threshold {
  text-align: right;
  color: #742a2a;
  font-size: 0.75rem;
  font-weight: 600;
  background: rgba(245, 101, 101, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
}

/* Top Songs Widget Styles */
.top-songs-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.top-song-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  color: white;
  position: relative;
  overflow: hidden;
  min-height: 50px;
  box-sizing: border-box;
}

.top-song-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
  pointer-events: none;
}

.song-rank {
  font-size: 1.25rem;
  font-weight: bold;
  margin-right: 12px;
  min-width: 32px;
  text-align: center;
  background: rgba(255,255,255,0.2);
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.top-song-item .song-info {
  flex: 1;
  margin-right: 12px;
}

.top-song-item .song-title {
  font-weight: 600;
  font-size: 0.8rem;
  margin-bottom: 2px;
  color: white;
}

.top-song-item .song-artist {
  color: rgba(255,255,255,0.8);
  font-size: 0.75rem;
}

.top-song-item .song-plays {
  text-align: right;
  background: rgba(255,255,255,0.2);
  padding: 6px 10px;
  border-radius: 6px;
}

.plays-count {
  font-size: 1.25rem;
  font-weight: bold;
  color: white;
  line-height: 1;
}

.plays-label {
  font-size: 0.75rem;
  color: rgba(255,255,255,0.8);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* General card styles for other pages */
.card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 15px rgba(0,0,0,0.1);
}

.card h2 {
  color: #2d3748;
  margin-bottom: 20px;
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .dashboard {
    grid-template-columns: 1fr;
  }
  
  .container {
    padding: 10px;
  }
  
  .header h1 {
    font-size: 2rem;
  }
}

/* Song Plays Widget Styles */
.song-plays-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.song-play-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 10px;
  background: #f0f9ff;
  border-radius: 6px;
  border-left: 3px solid #3b82f6;
  flex-shrink: 0;
  min-height: 50px;
  box-sizing: border-box;
}

.play-info {
  flex: 1;
}

.play-song {
  display: flex;
  flex-direction: column;
  margin-bottom: 2px;
}

.play-title {
  font-weight: 600;
  color: #1e40af;
  font-size: 0.8rem;
  margin-bottom: 1px;
}

.play-artist {
  color: #64748b;
  font-size: 0.75rem;
}

.play-user {
  color: #64748b;
  font-size: 0.75rem;
  font-style: italic;
}

.play-time {
  text-align: right;
  color: #64748b;
  font-size: 0.75rem;
  font-family: monospace;
  background: rgba(59, 130, 246, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
} 