package com.example;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.streaming.api.functions.windowing.WindowFunction;
import org.apache.flink.streaming.api.windowing.assigners.TumblingProcessingTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.util.Collector;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.List;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.stream.Collectors;

public class MusicAnalyticsJob {
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    public static void main(String[] args) throws Exception {
        // Set up the streaming execution environment
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        // Kafka source configuration
        ParameterTool parameterTool = ParameterTool.fromArgs(args);
        String bootstrapServers = parameterTool.get("bootstrap.servers", 
            System.getenv("KAFKA_BROKERS") != null ? System.getenv("KAFKA_BROKERS") : "kafka:9092");

        // Debug logging
        System.out.println("=== MUSIC ANALYTICS JOB DEBUG INFO ===");
        System.out.println("Raw args: " + java.util.Arrays.toString(args));
        System.out.println("ParameterTool has bootstrap.servers: " + parameterTool.has("bootstrap.servers"));
        System.out.println("ParameterTool bootstrap.servers value: " + parameterTool.get("bootstrap.servers", "NOT_FOUND"));
        System.out.println("KAFKA_BROKERS environment variable: " + System.getenv("KAFKA_BROKERS"));
        System.out.println("Final bootstrap servers: " + bootstrapServers);
        System.out.println("=== END DEBUG INFO ===");

        // Kafka source
        KafkaSource<String> source = KafkaSource.<String>builder()
                .setBootstrapServers(bootstrapServers)
                .setTopics("song_plays")
                .setGroupId("music-analytics-job")
                .setStartingOffsets(OffsetsInitializer.latest())
                .setValueOnlyDeserializer(new SimpleStringSchema())
                .setProperty("client.dns.lookup", "use_all_dns_ips")
                .build();

        // Create the main stream
        DataStream<String> kafkaStream = env.fromSource(source, 
            org.apache.flink.api.common.eventtime.WatermarkStrategy.noWatermarks(), 
            "Kafka Source");

        // Parse JSON to SongPlay objects
        DataStream<SongPlay> songPlays = kafkaStream
            .process(new ProcessFunction<String, SongPlay>() {
                @Override
                public void processElement(String value, Context ctx, Collector<SongPlay> out) throws Exception {
                    try {
                        SongPlay songPlay = objectMapper.readValue(value, SongPlay.class);
                        System.out.println("=== RECEIVED MESSAGE (MusicAnalyticsJob) ===");
                        System.out.println("Message: " + value);
                        System.out.println("Timestamp: " + System.currentTimeMillis());
                        System.out.println("===========================================");
                        out.collect(songPlay);
                    } catch (Exception e) {
                        System.err.println("Error parsing JSON: " + value + ", Error: " + e.getMessage());
                    }
                }
            })
            .name("Parse JSON");

        // Song Metrics Stream - Calculate metrics per song in 1-minute windows
        DataStream<SongMetrics> songMetrics = songPlays
            .keyBy(SongPlay::getSongId)
            .window(TumblingProcessingTimeWindows.of(Time.minutes(1)))
            .apply(new SongMetricsWindowFunction())
            .name("Song Metrics");

        // Bot Detection Stream - Detect users with too many plays in 1-minute windows
        DataStream<BotAlert> botAlerts = songPlays
            .keyBy(SongPlay::getUserId)
            .window(TumblingProcessingTimeWindows.of(Time.minutes(1)))
            .apply(new BotDetectionWindowFunction())
            .filter(alert -> alert != null)
            .name("Bot Detection");

        // Top Songs Stream - Track cumulative play counts and generate top 5
        DataStream<TopSongs> topSongs = songPlays
            .keyBy(SongPlay::getSongId)
            .process(new TopSongsProcessFunction())
            .name("Top Songs Tracking");

        // Kafka sinks
        KafkaSink<String> songMetricsSink = KafkaSink.<String>builder()
            .setBootstrapServers(bootstrapServers)
            .setRecordSerializer(KafkaRecordSerializationSchema.builder()
                .setTopic("song_metrics")
                .setValueSerializationSchema(new SimpleStringSchema())
                .build())
            .build();

        KafkaSink<String> botAlertsSink = KafkaSink.<String>builder()
            .setBootstrapServers(bootstrapServers)
            .setRecordSerializer(KafkaRecordSerializationSchema.builder()
                .setTopic("bot_alerts")
                .setValueSerializationSchema(new SimpleStringSchema())
                .build())
            .build();

        KafkaSink<String> topSongsSink = KafkaSink.<String>builder()
            .setBootstrapServers(bootstrapServers)
            .setRecordSerializer(KafkaRecordSerializationSchema.builder()
                .setTopic("top_songs")
                .setValueSerializationSchema(new SimpleStringSchema())
                .build())
            .build();

        // Send song metrics to Kafka
        songMetrics
            .map(metrics -> {
                try {
                    return objectMapper.writeValueAsString(metrics);
                } catch (Exception e) {
                    System.err.println("Error serializing song metrics: " + e.getMessage());
                    return null;
                }
            })
            .filter(metrics -> metrics != null)
            .sinkTo(songMetricsSink)
            .name("Song Metrics Sink");

        // Send bot alerts to Kafka
        botAlerts
            .map(alert -> {
                try {
                    return objectMapper.writeValueAsString(alert);
                } catch (Exception e) {
                    System.err.println("Error serializing bot alert: " + e.getMessage());
                    return null;
                }
            })
            .filter(alert -> alert != null)
            .sinkTo(botAlertsSink)
            .name("Bot Alerts Sink");

        // Send top songs to Kafka
        topSongs
            .map(topSongsData -> {
                try {
                    return objectMapper.writeValueAsString(topSongsData);
                } catch (Exception e) {
                    System.err.println("Error serializing top songs: " + e.getMessage());
                    return null;
                }
            })
            .filter(topSongsData -> topSongsData != null)
            .sinkTo(topSongsSink)
            .name("Top Songs Sink");

        // Execute the job
        env.execute("Music Analytics Job");
    }

    // Song Metrics Window Function
    public static class SongMetricsWindowFunction implements WindowFunction<SongPlay, SongMetrics, String, TimeWindow> {
        @Override
        public void apply(String songId, TimeWindow window, Iterable<SongPlay> input, Collector<SongMetrics> out) throws Exception {
            String songTitle = null;
            String artist = null;
            long totalPlays = 0;
            Set<String> uniqueUsers = new HashSet<>();
            
            for (SongPlay songPlay : input) {
                if (songTitle == null) {
                    songTitle = songPlay.getSongTitle();
                    artist = songPlay.getArtist();
                }
                totalPlays += songPlay.getPlayCount();
                uniqueUsers.add(songPlay.getUserId());
            }
            
            if (songTitle != null) {
                SongMetrics metrics = new SongMetrics(songId, songTitle, artist, 
                                                    totalPlays, uniqueUsers.size(), 
                                                    window.getStart(), window.getEnd());
                out.collect(metrics);
                System.out.println("Produced song metrics: " + metrics);
            }
        }
    }

    // Bot Detection Window Function
    public static class BotDetectionWindowFunction implements WindowFunction<SongPlay, BotAlert, String, TimeWindow> {
        private static final long BOT_THRESHOLD = 10; // Alert if user has more than 10 plays in 1 minute
        
        @Override
        public void apply(String userId, TimeWindow window, Iterable<SongPlay> input, Collector<BotAlert> out) throws Exception {
            long totalPlays = 0;
            
            for (SongPlay songPlay : input) {
                totalPlays += songPlay.getPlayCount();
            }
            
            if (totalPlays > BOT_THRESHOLD) {
                BotAlert alert = new BotAlert(userId, totalPlays, 60000, BOT_THRESHOLD);
                out.collect(alert);
                System.out.println("Produced bot alert: " + alert);
            }
        }
    }

    // Top Songs Process Function - Tracks cumulative play counts and generates top 5
    public static class TopSongsProcessFunction extends KeyedProcessFunction<String, SongPlay, TopSongs> {
        private ValueState<Long> playCountState;
        private ValueState<String> songTitleState;
        private ValueState<String> artistState;
        
        // Global state to track all songs (this is a simplified approach)
        // In a production environment, you might use a different approach like a global window
        private static final Map<String, TopSongs.SongPlayCount> allSongs = new HashMap<>();
        
        @Override
        public void open(Configuration parameters) throws Exception {
            playCountState = getRuntimeContext().getState(
                new ValueStateDescriptor<>("playCount", Long.class));
            songTitleState = getRuntimeContext().getState(
                new ValueStateDescriptor<>("songTitle", String.class));
            artistState = getRuntimeContext().getState(
                new ValueStateDescriptor<>("artist", String.class));
        }
        
        @Override
        public void processElement(SongPlay songPlay, Context ctx, Collector<TopSongs> out) throws Exception {
            // Update state for this song
            Long currentCount = playCountState.value();
            if (currentCount == null) {
                currentCount = 0L;
            }
            currentCount += songPlay.getPlayCount();
            playCountState.update(currentCount);
            
            // Store song metadata
            if (songTitleState.value() == null) {
                songTitleState.update(songPlay.getSongTitle());
                artistState.update(songPlay.getArtist());
            }
            
            // Update global tracking
            String songId = ctx.getCurrentKey();
            allSongs.put(songId, new TopSongs.SongPlayCount(
                songId, 
                songPlay.getSongTitle(), 
                songPlay.getArtist(), 
                currentCount
            ));
            
            // Generate top 5 songs
            List<TopSongs.SongPlayCount> topSongsList = allSongs.values().stream()
                .sorted(Comparator.comparing(TopSongs.SongPlayCount::getTotalPlays).reversed())
                .limit(5)
                .collect(Collectors.toList());
            
            TopSongs topSongs = new TopSongs(topSongsList, System.currentTimeMillis());
            out.collect(topSongs);
            
            System.out.println("Produced top songs: " + topSongs);
        }
    }
} 