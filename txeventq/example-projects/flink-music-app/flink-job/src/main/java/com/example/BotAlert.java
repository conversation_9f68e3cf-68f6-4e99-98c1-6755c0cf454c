package com.example;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.Instant;

public class BotAlert {
    @JsonProperty("userId")
    private String userId;
    
    @JsonProperty("totalPlays")
    private long totalPlays;
    
    @JsonProperty("timeWindow")
    private long timeWindow; // in milliseconds
    
    @JsonProperty("threshold")
    private long threshold;
    
    @JsonProperty("timestamp")
    private long timestamp;

    public BotAlert() {}

    public BotAlert(String userId, long totalPlays, long timeWindow, long threshold) {
        this.userId = userId;
        this.totalPlays = totalPlays;
        this.timeWindow = timeWindow;
        this.threshold = threshold;
        this.timestamp = Instant.now().toEpochMilli();
    }

    // Getters and setters
    public String getUserId() { return userId; }
    public void setUserId(String userId) { this.userId = userId; }
    
    public long getTotalPlays() { return totalPlays; }
    public void setTotalPlays(long totalPlays) { this.totalPlays = totalPlays; }
    
    public long getTimeWindow() { return timeWindow; }
    public void setTimeWindow(long timeWindow) { this.timeWindow = timeWindow; }
    
    public long getThreshold() { return threshold; }
    public void setThreshold(long threshold) { this.threshold = threshold; }
    
    public long getTimestamp() { return timestamp; }
    public void setTimestamp(long timestamp) { this.timestamp = timestamp; }

    @Override
    public String toString() {
        return String.format("BotAlert{userId='%s', totalPlays=%d, timeWindow=%d, threshold=%d}",
                userId, totalPlays, timeWindow, threshold);
    }
} 