const { Kafka } = require('kafkajs');
const WebSocket = require('ws');
const http = require('http');
const express = require('express');

// Configuration
const KAFKA_BROKERS = process.env.KAFKA_BROKERS || 'kafka:9092';
const PORT = process.env.PORT || 8080;
const SONG_PLAYS_TOPIC = 'song_plays';

// Initialize Kafka client
const kafka = new Kafka({
  clientId: 'ws-backend',
  brokers: KAFKA_BROKERS.split(','),
});

// Create consumers
const songMetricsConsumer = kafka.consumer({ groupId: 'ws-song-metrics-group' });
const botAlertsConsumer = kafka.consumer({ groupId: 'ws-bot-alerts-group' });
const topSongsConsumer = kafka.consumer({ groupId: 'ws-top-songs-group' });
const songPlaysConsumer = kafka.consumer({ groupId: 'ws-song-plays-group' });

// --- Express App Setup ---
const app = express();
app.use(express.json());

// CORS Middleware (simple version for development)
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
  next();
});

const server = http.createServer(app);
const wss = new WebSocket.Server({ server });

console.log(`HTTP and WebSocket server starting on port ${PORT}`);
console.log(`Connecting to Kafka brokers: ${KAFKA_BROKERS}`);

// --- Kafka Producer Logic ---

// Shared song data (matching Java producer)
const SONGS = [
    { songId: "song_001", songTitle: "Bohemian Rhapsody", artist: "Queen", duration: 354 },
    { songId: "song_002", songTitle: "Hotel California", artist: "Eagles", duration: 391 },
    { songId: "song_003", songTitle: "Stairway to Heaven", artist: "Led Zeppelin", duration: 482 },
    { songId: "song_004", songTitle: "Imagine", artist: "John Lennon", duration: 183 },
    { songId: "song_005", songTitle: "Hey Jude", artist: "The Beatles", duration: 431 },
    { songId: "song_006", songTitle: "Smells Like Teen Spirit", artist: "Nirvana", duration: 301 },
    { songId: "song_007", songTitle: "Like a Rolling Stone", artist: "Bob Dylan", duration: 369 },
    { songId: "song_008", songTitle: "I Can't Get No Satisfaction", artist: "The Rolling Stones", duration: 224 },
    { songId: "song_009", songTitle: "God Only Knows", artist: "The Beach Boys", duration: 172 },
    { songId: "song_010", songTitle: "Yesterday", artist: "The Beatles", duration: 125 },
    { songId: "song_011", songTitle: "Good Vibrations", artist: "The Beach Boys", duration: 216 },
    { songId: "song_012", songTitle: "Johnny B. Goode", artist: "Chuck Berry", duration: 161 },
    { songId: "song_013", songTitle: "What's Going On", artist: "Marvin Gaye", duration: 232 },
    { songId: "song_014", songTitle: "My Generation", artist: "The Who", duration: 224 },
    { songId: "song_015", songTitle: "A Change Is Gonna Come", artist: "Sam Cooke", duration: 191 }
];

async function produceSongPlays(count = 1) {
  const producer = kafka.producer();
  await producer.connect();

  const users = ["user_001", "user_002", "user_003", "user_004", "user_005", "user_006", "user_007", "user_008", "user_009", "user_010"];
  
  const producedMessages = [];

  for (let i = 0; i < count; i++) {
    const songData = SONGS[Math.floor(Math.random() * SONGS.length)];
    const userId = users[Math.floor(Math.random() * users.length)];
    const timestamp = Date.now();

    const message = {
      userId: userId,
      songId: songData.songId,
      songTitle: songData.songTitle,
      artist: songData.artist,
      timestamp: timestamp,
      duration: songData.duration,
      playCount: 1, // Each event produced from the UI is a single play
    };

    await producer.send({
      topic: SONG_PLAYS_TOPIC,
      messages: [{ value: JSON.stringify(message) }],
    });

    producedMessages.push(message);
  }
  
  console.log(`Produced ${count} messages to ${SONG_PLAYS_TOPIC}`);
  await producer.disconnect();
  return producedMessages;
}

// --- API Endpoints ---
app.post('/produce', async (req, res) => {
  try {
    const { count = 1 } = req.body;
    const numCount = parseInt(count, 10);

    if (isNaN(numCount) || numCount <= 0 || numCount > 1000) {
      return res.status(400).json({ error: 'Invalid count. Must be a number between 1 and 1000.' });
    }

    const messages = await produceSongPlays(numCount);
    res.status(200).json({
      message: `Successfully produced ${numCount} events.`,
      events: messages,
    });
  } catch (error) {
    console.error('Failed to produce events:', error);
    res.status(500).json({ error: 'Failed to produce events.' });
  }
});

// Store connected clients
const clients = new Set();

// WebSocket connection handling
wss.on('connection', (ws) => {
  console.log('New WebSocket client connected');
  clients.add(ws);

  // Send welcome message
  ws.send(JSON.stringify({
    type: 'connection',
    message: 'Connected to real-time music analytics',
    timestamp: new Date().toISOString()
  }));

  // Handle client disconnect
  ws.on('close', () => {
    console.log('WebSocket client disconnected');
    clients.delete(ws);
  });

  // Handle client errors
  ws.on('error', (error) => {
    console.error('WebSocket error:', error);
    clients.delete(ws);
  });
});

// Broadcast message to all connected clients
function broadcast(message) {
  const messageStr = JSON.stringify(message);
  clients.forEach((client) => {
    if (client.readyState === WebSocket.OPEN) {
      client.send(messageStr);
    }
  });
}

// Kafka consumer functions
async function consumeSongMetrics() {
  try {
    await songMetricsConsumer.connect();
    await songMetricsConsumer.subscribe({ topic: 'song_metrics', fromBeginning: false });
    
    songMetricsConsumer.run({
      eachMessage: async ({ topic, partition, message }) => {
        try {
          const songMetrics = JSON.parse(message.value.toString());
          console.log('Received song metrics:', songMetrics.songTitle);
          
          broadcast({
            type: 'song_metrics',
            data: songMetrics,
            timestamp: new Date().toISOString()
          });
        } catch (error) {
          console.error('Error processing song metrics:', error);
        }
      },
    });
  } catch (error) {
    console.error('Error in song metrics consumer:', error);
    throw error;
  }
}

async function consumeBotAlerts() {
  try {
    await botAlertsConsumer.connect();
    await botAlertsConsumer.subscribe({ topic: 'bot_alerts', fromBeginning: false });
    
    botAlertsConsumer.run({
      eachMessage: async ({ topic, partition, message }) => {
        try {
          const botAlert = JSON.parse(message.value.toString());
          console.log('Received bot alert for user:', botAlert.userId);
          
          broadcast({
            type: 'bot_alert',
            data: botAlert,
            timestamp: new Date().toISOString()
          });
        } catch (error) {
          console.error('Error processing bot alert:', error);
        }
      },
    });
  } catch (error) {
    console.error('Error in bot alerts consumer:', error);
    throw error;
  }
}

async function consumeTopSongs() {
  try {
    await topSongsConsumer.connect();
    await topSongsConsumer.subscribe({ topic: 'top_songs', fromBeginning: false });
    
    topSongsConsumer.run({
      eachMessage: async ({ topic, partition, message }) => {
        try {
          const topSongs = JSON.parse(message.value.toString());
          console.log('Received top songs update with', topSongs.topSongs?.length || 0, 'songs');
          
          broadcast({
            type: 'top_songs',
            data: topSongs,
            timestamp: new Date().toISOString()
          });
        } catch (error) {
          console.error('Error processing top songs:', error);
        }
      },
    });
  } catch (error) {
    console.error('Error in top songs consumer:', error);
    throw error;
  }
}

async function consumeSongPlays() {
  try {
    await songPlaysConsumer.connect();
    await songPlaysConsumer.subscribe({ topic: 'song_plays', fromBeginning: false });
    
    songPlaysConsumer.run({
      eachMessage: async ({ topic, partition, message }) => {
        try {
          const songPlay = JSON.parse(message.value.toString());
          console.log('Received song play:', songPlay.songTitle, 'by', songPlay.userId);
          
          broadcast({
            type: 'song_play',
            data: songPlay,
            timestamp: new Date().toISOString()
          });
        } catch (error) {
          console.error('Error processing song play:', error);
        }
      },
    });
  } catch (error) {
    console.error('Error in song plays consumer:', error);
    throw error;
  }
}

// Graceful shutdown
async function shutdown() {
  console.log('Shutting down...');
  
  // Close WebSocket server
  server.close();
  
  // Disconnect Kafka consumers
  try {
    await songMetricsConsumer.disconnect();
    await botAlertsConsumer.disconnect();
    await topSongsConsumer.disconnect();
    await songPlaysConsumer.disconnect();
  } catch (error) {
    console.error('Error disconnecting Kafka consumers:', error);
  }
  
  process.exit(0);
}

// Handle shutdown signals
process.on('SIGTERM', shutdown);
process.on('SIGINT', shutdown);

// Start consumers
async function start() {
  try {
    console.log('=== STARTING KAFKA CONSUMERS ===');
    
    // Start all consumers in parallel
    console.log('Starting song metrics consumer...');
    consumeSongMetrics().catch(error => {
      console.error('Error in song metrics consumer:', error);
    });
    
    console.log('Starting bot alerts consumer...');
    consumeBotAlerts().catch(error => {
      console.error('Error in bot alerts consumer:', error);
    });
    
    console.log('Starting top songs consumer...');
    consumeTopSongs().catch(error => {
      console.error('Error in top songs consumer:', error);
    });
    
    console.log('Starting song plays consumer...');
    consumeSongPlays().catch(error => {
      console.error('Error in song plays consumer:', error);
    });
    
    console.log('=== ALL KAFKA CONSUMERS STARTED SUCCESSFULLY ===');
  } catch (error) {
    console.error('Error starting consumers:', error);
    console.error('Error stack:', error.stack);
    process.exit(1);
  }
}

// Start the application
console.log('About to call start() function...');
start().catch(error => {
  console.error('Error in start function:', error);
  console.error('Error stack:', error.stack);
  process.exit(1);
});

// Start the server
server.listen(PORT, () => {
    console.log(`Server is listening on port ${PORT}`);
}); 