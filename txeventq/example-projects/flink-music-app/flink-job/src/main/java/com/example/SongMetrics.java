package com.example;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.Instant;

public class SongMetrics {
    @JsonProperty("songId")
    private String songId;
    
    @JsonProperty("songTitle")
    private String songTitle;
    
    @JsonProperty("artist")
    private String artist;
    
    @JsonProperty("totalPlays")
    private long totalPlays;
    
    @JsonProperty("uniqueUsers")
    private long uniqueUsers;
    
    @JsonProperty("windowStart")
    private long windowStart;
    
    @JsonProperty("windowEnd")
    private long windowEnd;
    
    @JsonProperty("timestamp")
    private long timestamp;

    public SongMetrics() {}

    public SongMetrics(String songId, String songTitle, String artist, long totalPlays, long uniqueUsers, long windowStart, long windowEnd) {
        this.songId = songId;
        this.songTitle = songTitle;
        this.artist = artist;
        this.totalPlays = totalPlays;
        this.uniqueUsers = uniqueUsers;
        this.windowStart = windowStart;
        this.windowEnd = windowEnd;
        this.timestamp = Instant.now().toEpochMilli();
    }

    // Getters and setters
    public String getSongId() { return songId; }
    public void setSongId(String songId) { this.songId = songId; }
    
    public String getSongTitle() { return songTitle; }
    public void setSongTitle(String songTitle) { this.songTitle = songTitle; }
    
    public String getArtist() { return artist; }
    public void setArtist(String artist) { this.artist = artist; }
    
    public long getTotalPlays() { return totalPlays; }
    public void setTotalPlays(long totalPlays) { this.totalPlays = totalPlays; }
    
    public long getUniqueUsers() { return uniqueUsers; }
    public void setUniqueUsers(long uniqueUsers) { this.uniqueUsers = uniqueUsers; }
    
    public long getWindowStart() { return windowStart; }
    public void setWindowStart(long windowStart) { this.windowStart = windowStart; }
    
    public long getWindowEnd() { return windowEnd; }
    public void setWindowEnd(long windowEnd) { this.windowEnd = windowEnd; }
    
    public long getTimestamp() { return timestamp; }
    public void setTimestamp(long timestamp) { this.timestamp = timestamp; }

    @Override
    public String toString() {
        return String.format("SongMetrics{songId='%s', songTitle='%s', artist='%s', totalPlays=%d, uniqueUsers=%d, windowStart=%d, windowEnd=%d}",
                songId, songTitle, artist, totalPlays, uniqueUsers, windowStart, windowEnd);
    }
} 