import React, { useState, useEffect } from 'react';

function Producer() {
  const [logs, setLogs] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  // Load logs from localStorage on component mount
  useEffect(() => {
    const savedLogs = localStorage.getItem('producerLogs');
    if (savedLogs) {
      try {
        setLogs(JSON.parse(savedLogs));
      } catch (error) {
        console.error('Error loading logs from localStorage:', error);
      }
    }
  }, []);

  // Save logs to localStorage whenever logs change
  useEffect(() => {
    localStorage.setItem('producerLogs', JSON.stringify(logs));
  }, [logs]);

  const produceEvents = async (count) => {
    setIsLoading(true);
    setLogs(prev => [`[${new Date().toLocaleTimeString()}] Producing ${count} events...`, ...prev]);
    
    try {
      const response = await fetch('/api/produce', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ count }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to produce events.');
      }
      
      setLogs(prev => [
        `[${new Date().toLocaleTimeString()}] ✅ Successfully produced ${count} events.`,
        ...data.events.map(e => `  - User: ${e.userId}, Song: "${e.songTitle}" by ${e.artist}`),
        ...prev
      ]);

    } catch (error) {
      setLogs(prev => [`[${new Date().toLocaleTimeString()}] ❌ Error: ${error.message}`, ...prev]);
    } finally {
      setIsLoading(false);
    }
  };

  const clearLogs = () => {
    setLogs([]);
    localStorage.removeItem('producerLogs');
  };

  return (
    <div className="container">
      <div className="header">
        <h1> Kafka Event Producer</h1>
        <p>Use the buttons below to produce mock song play events.</p>
      </div>

      <div className="card">
        <h2>Produce Events</h2>
        <div className="producer-actions">
          <button onClick={() => produceEvents(1)} disabled={isLoading}>
            {isLoading ? 'Producing...' : 'Produce 1 Event'}
          </button>
          <button onClick={() => produceEvents(10)} disabled={isLoading}>
            {isLoading ? 'Producing...' : 'Produce 10 Events'}
          </button>
          <button onClick={() => produceEvents(100)} disabled={isLoading}>
            {isLoading ? 'Producing...' : 'Produce 100 Events'}
          </button>
        </div>
      </div>

      <div className="card">
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
          <h2>Production Log</h2>
          <button 
            onClick={clearLogs}
            style={{
              background: '#f56565',
              color: 'white',
              border: 'none',
              padding: '8px 16px',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            Clear Logs
          </button>
        </div>
        <div className="log-container">
          {logs.length === 0 ? (
            <p style={{ color: '#6b7280', textAlign: 'center', marginTop: '20px' }}>
              No events produced yet. Click the buttons above to start producing events.
            </p>
          ) : (
            logs.map((log, index) => (
              <div key={index} className={`log-message ${log.includes('❌') ? 'log-error' : ''}`}>
                {log}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}

export default Producer; 