# Real-Time Music Streaming Analytics Demo

This project is a hands-on demo showing how to process and analyze real-time music streaming data using **Apache Flink** and **Kafka**. It simulates users playing songs, detects bots, and displays live analytics in a web dashboard.

---

## 🚀 How to Run and Stop the App

### Prerequisites

- **Docker** and **Docker Compose** installed
- At least **4GB RAM** available

### Start Everything (Recommended)

```bash
# In your project directory:
./start-app.sh
```

- This script will build everything, start all services, create Kafka topics, deploy the Flink job, and launch the UI.
- Access the dashboard at: [http://localhost:3000](http://localhost:3000)

### Stop Everything

```bash
./stop-app.sh
```
- Stops all services, but keeps data (Kafka topics, Flink state).

To **fully clean up all data** (including Kafka topics and Flink state):

```bash
./stop-app.sh --clean
```

This script will:
- ✅ Check prerequisites (<PERSON>er, Docker Compose)
- ✅ Build all Docker images
- ✅ Start infrastructure (Zookeeper, Kafka, Flink)
- ✅ Create Kafka topics automatically
- ✅ Deploy the Flink job
- ✅ Start application services (UI, WebSocket backend, producer)
- ✅ Provide status and access information

---

## 🏗️ What Does This App Do?

**Architecture Overview:**

```
[Event Producer] → [Kafka] → [Flink Job] → [Kafka] → [WebSocket Server] → [React UI]
```

- **Event Producer:** Simulates users playing songs and sends events to Kafka.
- **Kafka:** Message broker for real-time data with four topics:
  - `song_plays`: Input topic for raw song play events
  - `song_metrics`: Output topic for aggregated song statistics
  - `bot_alerts`: Output topic for bot detection alerts
  - `top_songs`: Output topic for cumulative top songs tracking
- **Flink Job:** Processes events in real time (see below for Flink features).
- **WebSocket Server:** Forwards processed results to the UI.
- **React UI:** Shows live charts and bot alerts.

### Kafka Topics Overview

The application uses four Kafka topics to manage data flow:

**📥 Input Topic:**
- **`song_plays`**: Receives raw song play events from the producer

**📤 Output Topics:**
- **`song_metrics`**: Aggregated song statistics from Flink
- **`bot_alerts`**: Bot detection alerts from Flink  
- **`top_songs`**: Cumulative top songs tracking from Flink

**⚙️ Topic Configuration:**
- **Partitions:** 3 per topic (enables parallel processing)
- **Auto-creation:** Topics are automatically created by the startup script

---

## ✨ What Flink Features Are Used?

- **Stream Processing:** Flink reads song play events from Kafka as a continuous stream.
- **Windowed Aggregation:** Calculates the most popular songs in sliding 1-minute windows.
- **Stateful Processing:** Remembers user activity to detect bots (users with suspiciously high play counts).
- **Side Outputs:** Sends bot alerts to a separate Kafka topic.
- **Kafka Integration:** Reads from and writes to Kafka topics in real time.

---

## ⚙️ Flink Configuration & Scaling

### Current Configuration

**Memory Settings:**
```yaml
jobmanager.memory.process.size: 1600m    # JobManager memory
taskmanager.memory.process.size: 1728m   # TaskManager memory
taskmanager.numberOfTaskSlots: 1         # Slots per TaskManager
parallelism.default: 1                   # Default parallelism
```

**State Backend:**
```yaml
state.backend: hashmap                   # In-memory state storage
```

**Network & Communication:**
```yaml
jobmanager.rpc.address: flink-jobmanager  # JobManager hostname
jobmanager.rpc.port: 6123                 # RPC communication port
rest.bind-port: 8081                      # Web UI port
```

### State Backends Explained

**1. HashMap State Backend (Current)**
- **What it is:** Stores all state in JVM heap memory
- **Pros:** Fastest performance, simple setup
- **Cons:** State lost on restart, limited by available memory
- **Best for:** Development, demos, small datasets

**2. RocksDB State Backend**
- **What it is:** Stores state on disk using RocksDB
- **Pros:** Persistent state, handles large datasets, memory efficient
- **Cons:** Slower than HashMap, requires disk I/O
- **Best for:** Production, large state, fault tolerance

**3. FsStateBackend**
- **What it is:** Hybrid approach - metadata in memory, data on disk
- **Pros:** Good balance of performance and persistence
- **Cons:** Still limited by memory for metadata
- **Best for:** Medium-sized state, production workloads

### Flink Architecture Components

**1. JobManager (Current: 1600MB)**
- **What it does:** Coordinates the entire Flink job
- **Responsibilities:** 
  - Schedules tasks to TaskManagers
  - Manages checkpoints and savepoints
  - Handles job failures and recovery
  - Provides web UI for monitoring
- **Scaling:** Usually 1 JobManager per cluster (can have standby for HA)

**2. TaskManager (Current: 1728MB)**
- **What it does:** Executes the actual data processing tasks
- **Responsibilities:**
  - Runs parallel instances of operators
  - Manages local state and memory
  - Reports status to JobManager
- **Scaling:** Add more TaskManagers for horizontal scaling

**3. Task Slots (Current: 1 slot per TaskManager)**
- **What it is:** A unit of resource allocation within a TaskManager
- **Current setup:** 1 TaskManager × 1 slot = 1 total slot
- **Scaling:** Increase slots per TaskManager or add more TaskManagers

### Team Analogy for Understanding Flink

Think of a Flink cluster like a team working on a project:

**JobManager = Project Manager**
- Plans the work, assigns tasks, and monitors progress
- Doesn't do the actual work but coordinates everything
- Usually only one per project (can have backup for high availability)

**TaskManager = Team Members**
- Actually do the work assigned by the project manager
- Can handle multiple tasks simultaneously
- More team members = more work can be done in parallel

**Task Slots = Individual Work Capacity**
- Like how many projects a team member can work on at once
- Each slot can run one copy of your processing logic
- More slots = more parallel processing capacity

**Parallelism = Team Size**
- How many team members are working on the same type of task
- Must match the available work capacity (task slots)
- More parallelism = faster processing but more coordination overhead

### Tasks vs Parallelism - Clear Explanation

**What is a Task?**
- A **Task** is a single instance of an operator (like map, filter, window) running on one TaskManager
- Think of it as "one copy of your processing logic"

**What is Parallelism?**
- **Parallelism** is how many copies of the same operator you want to run simultaneously
- Think of it as "how many workers are processing the same type of data"

**Simple Example:**
```
Imagine you're processing customer orders:

TASK = One worker processing orders
PARALLELISM = How many workers you have doing the same job

Parallelism = 1: One worker handles all orders
Parallelism = 4: Four workers handle orders (each gets 1/4 of the orders)
```

**Key Rules:**
1. **Parallelism cannot exceed available Task Slots**
2. **Each parallel instance runs in its own Task Slot**
3. **Data is automatically partitioned across parallel instances**

### Quick Scaling Guide

**Increase Parallelism:**
```yaml
# Option 1: Increase slots per TaskManager
taskmanager.numberOfTaskSlots: 4
parallelism.default: 4

# Option 2: Add more TaskManagers (horizontal scaling)
# Add to docker-compose.yml:
flink-taskmanager-2:
  image: flink:1.18.1
  command: taskmanager
  environment:
    - FLINK_PROPERTIES=jobmanager.rpc.address: flink-jobmanager
      taskmanager.numberOfTaskSlots: 2
      taskmanager.memory.process.size: 4g
```

**What Happens When You Scale:**
- ✅ **Better Performance:** More CPU cores utilized
- ✅ **Higher Throughput:** Can process more events per second
- ✅ **Better Resource Utilization:** Distributes load across multiple slots
- ⚠️ **More Memory:** Each parallel instance needs its own state
- ⚠️ **Network Overhead:** Data shuffling between parallel instances
- ⚠️ **Complexity:** Harder to debug and monitor

### Keyed vs Non-Keyed Operations

**Current Job Uses Keyed Operations:**
```java
// Keyed by songId for metrics
.keyBy(SongPlay::getSongId)

// Keyed by userId for bot detection  
.keyBy(SongPlay::getUserId)
```

**Why Keyed Operations:**
- **Partitioning:** Data with same key goes to same parallel instance
- **State Management:** State is partitioned by key
- **Scalability:** Each key can be processed independently

**Scaling Considerations:**
- **Hot Keys:** If one song gets 90% of plays, that parallel instance becomes bottleneck
- **Key Distribution:** Even key distribution = better parallelism
- **State Size:** Each key maintains its own state

### Window Types

**Current: Tumbling Windows (1-minute)**
```java
TumblingProcessingTimeWindows.of(Time.minutes(1))
```

**Other Window Options:**

**1. Sliding Windows**
```java
// 1-minute window, sliding every 30 seconds
SlidingProcessingTimeWindows.of(Time.minutes(1), Time.seconds(30))
```

**2. Session Windows**
```java
// Windows based on user activity gaps
SessionWindows.withGap(Time.minutes(5))
```

**3. Global Windows**
```java
// Process all data together
GlobalWindows.create()
```

### Memory Tuning Basics

**Current Memory Allocation:**
- **JobManager:** 1600MB (coordinates the job)
- **TaskManager:** 1728MB (processes data)

**For Higher Throughput:**
```yaml
# Increase memory for better performance
jobmanager.memory.process.size: 4g
taskmanager.memory.process.size: 8g
taskmanager.numberOfTaskSlots: 4
```

**Memory Considerations:**
- **State Size:** More state = more memory needed per slot
- **Window Size:** Larger windows = more data in memory
- **Parallelism:** More parallelism = more memory instances
- **JVM Overhead:** Each TaskManager has ~200-500MB overhead

### Monitoring & Troubleshooting

**Key Metrics to Watch:**
- **Throughput:** Events processed per second
- **Latency:** Time from input to output
- **Backpressure:** When downstream can't keep up
- **State Size:** Memory usage for state
- **Task Slot Utilization:** How many slots are actively processing
- **Checkpoint Duration:** Time to complete checkpoints
- **Network Buffer Usage:** Memory used for data shuffling

**Flink Web UI Monitoring:**
- **Job Overview:** Overall job status and metrics
- **Task Managers:** Individual TaskManager status and resource usage
- **Job Graph:** Visual representation of your processing pipeline
- **Metrics:** Detailed performance metrics per operator

**Common Issues:**
- **Out of Memory:** Increase memory or switch to RocksDB
- **Backpressure:** Increase parallelism or optimize processing
- **Hot Keys:** Redistribute data or use different partitioning
- **Slow Checkpoints:** Optimize state backend or increase checkpoint interval
- **Task Slot Starvation:** Not enough slots for desired parallelism
- **Network Buffer Exhaustion:** Increase network buffer memory

**Debugging Commands:**
```bash
# Check TaskManager status
curl http://localhost:8081/taskmanagers

# Check job metrics
curl http://localhost:8081/jobs/<job-id>/metrics

# View TaskManager logs
docker-compose logs -f flink-taskmanager

# Check memory usage
docker stats flink-music-app-flink-taskmanager-1
```

---

## 🛠️ For Developers

- To build or run individual components, see the comments in each subfolder or the full README.
- To monitor Flink, visit [http://localhost:8081](http://localhost:8081).

---

## ❓ Need Help?

- If something doesn't work, try `./stop-app.sh --clean` and then `./start-app.sh` again.
- For more details, see the full README or open an issue.

## 🏗️ Architecture

```plaintext
[Console Producer] --("song_plays" topic)--> [Kafka Cluster] --(Flink Job)--> [Kafka topics: "song_metrics", "bot_alerts"] --(Node.js WS Server)--> [React UI]
```

### Components

1. **Console Producer (Java)**
   - Generates `SongPlay` events for multiple simulated users
   - Publishes to Kafka topic `song_plays`
   - Simulates bot behavior (10% chance of high play counts)

2. **Processing Job (Flink, Java)**
   - Source: Kafka connector from `song_plays`
   - Keyed streams by `userId` and `songId`
   - Tumbling window aggregation (Top songs in 1-minute windows)
   - Stateful bot detection (>10 plays in 1 minute, side output to `bot_alerts`)
   - Cumulative top songs tracking (all-time leaderboard)
   - Sink: Kafka to `song_metrics`, `bot_alerts`, and `top_songs`

3. **WebSocket Backend (Node.js)**
   - Kafka consumer on topics `song_metrics`, `bot_alerts`, `top_songs`, and `song_plays` via `kafkajs`
   - WebSocket server (`ws` library) to push events to connected clients
   - Real-time data forwarding to React UI

4. **Frontend (React)**
   - Connect via WebSocket to WS backend
   - Four live dashboard widgets:
     - **Top Songs Chart**: 1-minute window analytics with play counts and unique users
     - **Bot Detection Alerts**: Real-time alerts for suspicious activity
     - **Top 5 All Time**: Cumulative leaderboard of most played songs
     - **Recent Song Plays**: Live feed of last 20 individual play events

### Kafka Topics

The application uses three Kafka topics for data flow:

**Input Topic:**
- **`song_plays`**: Raw song play events from the producer
  - **Partitions:** 3
  - **Data:** Individual song play events (userId, songId, timestamp)

**Output Topics:**
- **`song_metrics`**: Aggregated song statistics from Flink
  - **Partitions:** 3
  - **Data:** Top songs with play counts and unique users per 1-minute window

- **`bot_alerts`**: Bot detection alerts from Flink
  - **Partitions:** 3
  - **Data:** Alerts for users with suspicious activity (>20 plays in 1 minute)

- **`top_songs`**: Cumulative top songs tracking from Flink
  - **Partitions:** 3
  - **Data:** Top 5 songs with cumulative play counts across all time

**Topic Configuration:**
- All topics use 3 partitions for parallel processing
- Topics are automatically created by the startup script

## 🛠️ Development

### Manual Setup & Run

If you prefer to run the setup manually:

1. **Clone and navigate to the project**
   ```bash
   cd flink-music-app
   ```

2. **Build all Docker images**
   ```bash
   docker-compose build
   ```

3. **Start the infrastructure**
   ```bash
   docker-compose up -d zookeeper kafka flink-jobmanager flink-taskmanager
   ```

4. **Wait for services to be ready (30-60 seconds)**
   ```bash
   docker-compose logs -f kafka
   # Wait until you see "started" messages
   ```

5. **Deploy the Flink job**
   ```bash
   # Copy the JAR to the Flink jobmanager
   docker cp flink-job/target/flink-job-1.0.0.jar flink-music-app-flink-jobmanager-1:/opt/flink/flink-job.jar
   
   # Submit the job via Flink CLI
   docker exec flink-music-app-flink-jobmanager-1 flink run /opt/flink/flink-job.jar
   ```

6. **Start the remaining services**
   ```bash
   docker-compose up -d ws-backend ui console-producer
   ```

7. **Open the dashboard**
   - Navigate to [http://localhost:3000](http://localhost:3000)
   - You should see the real-time dashboard with live data
## 🔧 Configuration

### Environment Variables

- `KAFKA_BROKERS`: Kafka broker addresses (default: `localhost:9092`)
- `PRODUCER_INTERVAL_MS`: Event generation interval (default: `1000ms`)
- `WS_PORT`: WebSocket server port (default: `3001`)
- `REACT_APP_WS_URL`: WebSocket URL for React app (default: `ws://localhost:3001`)

### Flink Job Management

```bash
# View Flink UI
open http://localhost:8081

# Submit job manually
docker exec flink-music-app_flink-jobmanager_1 flink run /opt/flink/flink-job.jar

# Cancel job
docker exec flink-music-app_flink-jobmanager_1 flink cancel <job-id>

# View job logs
docker-compose logs -f flink-taskmanager
```

## 🐛 Troubleshooting

### Common Issues

1. **Services not starting**
   ```bash
   # Check logs
   docker-compose logs
   
   # Restart specific service
   docker-compose restart <service-name>
   ```

2. **Flink job not processing data**
   ```bash
   # Check if topics exist
   docker exec flink-music-app_kafka_1 kafka-topics --list --bootstrap-server localhost:9092
   
   # Check Flink job status
   curl http://localhost:8081/jobs
   ```

3. **UI not receiving data**
   ```bash
   # Check WebSocket backend logs
   docker-compose logs -f ws-backend
   
   # Check browser console for WebSocket errors
   ```

4. **Out of memory errors**
   ```bash
   # Increase Docker memory limit to 4GB+
   # Or reduce Flink memory settings in docker-compose.yml
   ```

### Useful Commands

```bash
# View all logs
docker-compose logs -f

# Check Kafka topics
docker exec flink-music-app_kafka_1 kafka-topics --list --bootstrap-server localhost:9092

# Check topic messages
docker exec flink-music-app_kafka_1 kafka-console-consumer --bootstrap-server localhost:9092 --topic song_plays --from-beginning

# Restart everything
docker-compose down
docker-compose up -d --build
```

### Useful Scripts

```bash
# Check application status
docker-compose ps

# Stop the application
./stop-app.sh

# Stop and clean up all data
./stop-app.sh --clean

# Create Kafka topics manually (if needed)
./create-kafka-topics.sh

# Deploy Flink job manually (if needed)
./deploy-flink-job.sh
```