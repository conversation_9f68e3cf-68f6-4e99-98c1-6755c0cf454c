#!/bin/bash

# Real-Time Music Streaming Analytics Demo - Stop Script
# This script stops and cleans up the entire application

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to stop all services
stop_services() {
    print_status "Stopping all application services..."
    docker-compose down --remove-orphans
    print_success "All services stopped successfully"
}

# Function to clean up volumes (optional)
cleanup_volumes() {
    if [ "$1" = "--clean" ]; then
        print_warning "Cleaning up Docker volumes (this will delete all data)..."
        docker-compose down -v --remove-orphans
        print_success "Volumes cleaned up successfully"
    else
        print_status "Volumes preserved. Use --clean flag to remove volumes and data."
    fi
}

# Function to show running containers
show_status() {
    echo ""
    echo "📋 Current Docker containers:"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    echo ""
}

# Main execution
main() {
    echo "🎵 Real-Time Music Streaming Analytics Demo"
    echo "🛑 Stopping application..."
    echo ""
    
    # Check if containers are running
    if ! docker-compose ps | grep -q "Up"; then
        print_warning "No application containers are currently running."
        show_status
        exit 0
    fi
    
    # Stop services
    stop_services
    
    # Clean up volumes if requested
    cleanup_volumes "$1"
    
    # Show final status
    show_status
    
    print_success "Application stopped successfully!"
    echo ""
    echo "📝 To restart the application:"
    echo "   • ./start-app.sh"
    echo ""
}

# Run main function
main "$@" 