#!/bin/bash

# Create Kafka Topics Script
# This script creates the required Kafka topics for the music streaming analytics demo

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to wait for Kafka to be ready
wait_for_kafka() {
    print_status "Waiting for Kafka to be ready..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if docker exec flink-music-app-kafka-1 kafka-topics --list --bootstrap-server localhost:9092 >/dev/null 2>&1; then
            print_success "Kafka is ready!"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "Kafka failed to start within $((max_attempts * 2)) seconds"
    return 1
}

# Function to create a topic
create_topic() {
    local topic_name=$1
    local partitions=${2:-3}
    local replication_factor=${3:-1}
    
    print_status "Creating topic: $topic_name"
    
    docker exec flink-music-app-kafka-1 kafka-topics --create \
        --bootstrap-server localhost:9092 \
        --topic "$topic_name" \
        --partitions "$partitions" \
        --replication-factor "$replication_factor" \
        --if-not-exists
    
    print_success "Topic '$topic_name' created successfully"
}

# Function to list existing topics
list_topics() {
    print_status "Existing Kafka topics:"
    docker exec flink-music-app-kafka-1 kafka-topics --list --bootstrap-server localhost:9092
}

# Main execution
main() {
    echo "🎵 Real-Time Music Streaming Analytics Demo"
    echo "📝 Creating Kafka Topics"
    echo ""
    
    # Check if Kafka container is running
    if ! docker-compose ps | grep -q "kafka.*Up"; then
        print_error "Kafka is not running. Please start the infrastructure first:"
        echo "   docker-compose up -d zookeeper kafka"
        exit 1
    fi
    
    # Wait for Kafka to be ready
    if ! wait_for_kafka; then
        print_error "Failed to connect to Kafka. Please check the logs:"
        echo "   docker-compose logs kafka"
        exit 1
    fi
    
    # List existing topics
    echo ""
    list_topics
    echo ""
    
    # Create required topics
    print_status "Creating required topics..."
    
    create_topic "song_plays" 3 1
    create_topic "song_metrics" 3 1
    create_topic "bot_alerts" 3 1
    create_topic "top_songs" 3 1
    
    echo ""
    print_success "All topics created successfully!"
    echo ""
    
    # List topics again to confirm
    list_topics
    echo ""
    
    print_success "Kafka topics are ready for the application!"
    echo ""
    echo "📝 Next steps:"
    echo "   • Deploy Flink job: ./deploy-flink-job.sh"
    echo "   • Start application: docker-compose up -d ws-backend ui console-producer"
    echo "   • Access dashboard: http://localhost:3000"
    echo ""
}

# Run main function
main "$@" 