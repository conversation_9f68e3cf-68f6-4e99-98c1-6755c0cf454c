import React from 'react';
import { Routes, Route, Link, useLocation } from 'react-router-dom';
import Dashboard from './components/Dashboard';
import Producer from './components/Producer';
import './App.css';

function App() {
  const location = useLocation();

  const openInNewTab = (url) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  return (
    <div className="App">
      <nav className="navbar">
        <div className="navbar-brand">
          Real-Time Music App
        </div>
        <div className="navbar-links">
          <Link to="/" className={location.pathname === '/' ? 'active' : ''}>
            Analytics Dashboard
          </Link>
          <Link to="/produce" className={location.pathname === '/produce' ? 'active' : ''}>
            Event Producer
          </Link>
          <button 
            onClick={() => openInNewTab('http://localhost:8081/#/overview')}
            className="navbar-external-link"
          >
            Flink Dashboard
          </button>
          <button 
            onClick={() => openInNewTab('http://localhost:8082/topics')}
            className="navbar-external-link"
          >
            Kafka Dashboard
          </button>
        </div>
      </nav>

      <main>
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/produce" element={<Producer />} />
        </Routes>
      </main>
    </div>
  );
}

export default App; 