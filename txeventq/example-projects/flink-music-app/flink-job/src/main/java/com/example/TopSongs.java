package com.example;

import java.util.List;
import java.util.ArrayList;

public class TopSongs {
    private List<SongPlayCount> topSongs;
    private long timestamp;

    public TopSongs() {
        this.topSongs = new ArrayList<>();
        this.timestamp = System.currentTimeMillis();
    }

    public TopSongs(List<SongPlayCount> topSongs, long timestamp) {
        this.topSongs = topSongs;
        this.timestamp = timestamp;
    }

    public List<SongPlayCount> getTopSongs() {
        return topSongs;
    }

    public void setTopSongs(List<SongPlayCount> topSongs) {
        this.topSongs = topSongs;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    @Override
    public String toString() {
        return "TopSongs{" +
                "topSongs=" + topSongs +
                ", timestamp=" + timestamp +
                '}';
    }

    public static class SongPlayCount {
        private String songId;
        private String songTitle;
        private String artist;
        private long totalPlays;

        public SongPlayCount() {}

        public SongPlayCount(String songId, String songTitle, String artist, long totalPlays) {
            this.songId = songId;
            this.songTitle = songTitle;
            this.artist = artist;
            this.totalPlays = totalPlays;
        }

        public String getSongId() {
            return songId;
        }

        public void setSongId(String songId) {
            this.songId = songId;
        }

        public String getSongTitle() {
            return songTitle;
        }

        public void setSongTitle(String songTitle) {
            this.songTitle = songTitle;
        }

        public String getArtist() {
            return artist;
        }

        public void setArtist(String artist) {
            this.artist = artist;
        }

        public long getTotalPlays() {
            return totalPlays;
        }

        public void setTotalPlays(long totalPlays) {
            this.totalPlays = totalPlays;
        }

        @Override
        public String toString() {
            return "SongPlayCount{" +
                    "songId='" + songId + '\'' +
                    ", songTitle='" + songTitle + '\'' +
                    ", artist='" + artist + '\'' +
                    ", totalPlays=" + totalPlays +
                    '}';
        }
    }
} 