FROM openjdk:11-jdk-slim

WORKDIR /app

# Install Maven and dependencies in a single layer to reduce image size
RUN apt-get update && \
    apt-get install -y --no-install-recommends maven && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Copy Maven files first for better layer caching
COPY pom.xml .

# Download dependencies (this layer will be cached if pom.xml doesn't change)
RUN mvn dependency:go-offline -B

# Copy source code
COPY src ./src

# Build the project
RUN mvn clean package -DskipTests -B

# Remove Maven to reduce image size
RUN apt-get remove -y maven && \
    apt-get autoremove -y && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# The JAR should now exist in the target directory
RUN ls -la target/

# Keep the container running (the job will be submitted via Flink CLI)
CMD ["tail", "-f", "/dev/null"] 