#!/bin/bash

# Real-Time Music Streaming Analytics Demo - Startup Script
# This script sets up and starts the entire application

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Docker
    if ! command_exists docker; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check Docker Compose
    if ! command_exists docker-compose; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if Docker is running
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Function to build images
build_images() {
    print_status "Building Docker images..."
    docker-compose build
    print_success "All images built successfully"
}

# Function to start infrastructure
start_infrastructure() {
    print_status "Starting infrastructure services (Zookeeper, Kafka, Flink)..."
    docker-compose up -d zookeeper kafka flink-jobmanager flink-taskmanager
    
    # Wait for services to be ready
    print_status "Waiting for services to be ready..."
    sleep 30
    
    print_success "Infrastructure services started successfully"
}

# Function to create Kafka topics
create_topics() {
    print_status "Creating Kafka topics..."
    ./create-kafka-topics.sh
}

# Function to deploy Flink job
deploy_flink_job() {
    print_status "Deploying Flink job..."
    ./deploy-flink-job.sh
}

# Function to start application services
start_application() {
    print_status "Starting application services..."
    docker-compose up -d ws-backend ui console-producer
    
    print_success "Application services started successfully"
}

# Function to show status
show_status() {
    echo ""
    echo "🎉 ========================================="
    echo "🎉 Application Startup Complete!"
    echo "🎉 ========================================="
    echo ""
    echo "📊 Access Points:"
    echo "   • Dashboard: http://localhost:3000"
    echo "   • Flink UI: http://localhost:8081"
    echo "   • Kafka Console: http://localhost:8082"
    echo ""
    echo "📋 Services Status:"
    docker-compose ps
    echo ""
    echo "📝 Useful Commands:"
    echo "   • View logs: docker-compose logs -f"
    echo "   • Stop all: docker-compose down"
    echo "   • Restart: docker-compose restart"
    echo ""
    echo "🎵 The dashboard should start showing live data within 30-60 seconds!"
    echo ""
}

# Main execution
main() {
    echo "🎵 Real-Time Music Streaming Analytics Demo"
    echo "🚀 Starting complete application setup..."
    echo ""
    
    # Check prerequisites
    check_prerequisites
    
    # Build images
    build_images
    
    # Start infrastructure
    start_infrastructure
    
    # Create Kafka topics
    create_topics
    
    # Deploy Flink job
    deploy_flink_job
    
    # Start application services
    start_application
    
    # Show final status
    show_status
}

# Run main function
main "$@" 