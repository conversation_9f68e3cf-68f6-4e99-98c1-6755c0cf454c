package com.example;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.serialization.StringSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;
import java.util.Properties;
import java.util.Random;
import java.util.concurrent.TimeUnit;

public class SongPlayProducer {
    private static final Logger logger = LoggerFactory.getLogger(SongPlayProducer.class);
    private static final String TOPIC = "song_plays";
    
    private static final List<String> USER_IDS = Arrays.asList(
        "user_001", "user_002", "user_003", "user_004", "user_005",
        "user_006", "user_007", "user_008", "user_009", "user_010"
    );
    
    private static final List<SongData> SONGS = Arrays.asList(
        new SongData("song_001", "Bohemian Rhapsody", "Queen", 354),
        new SongData("song_002", "Hotel California", "Eagles", 391),
        new SongData("song_003", "Stairway to Heaven", "Led Zeppelin", 482),
        new SongData("song_004", "Imagine", "John Lennon", 183),
        new SongData("song_005", "Hey Jude", "The Beatles", 431),
        new SongData("song_006", "Smells Like Teen Spirit", "Nirvana", 301),
        new SongData("song_007", "Like a Rolling Stone", "Bob Dylan", 369),
        new SongData("song_008", "I Can't Get No Satisfaction", "The Rolling Stones", 224),
        new SongData("song_009", "God Only Knows", "The Beach Boys", 172),
        new SongData("song_010", "Yesterday", "The Beatles", 125),
        new SongData("song_011", "Good Vibrations", "The Beach Boys", 216),
        new SongData("song_012", "Johnny B. Goode", "Chuck Berry", 161),
        new SongData("song_013", "What's Going On", "Marvin Gaye", 232),
        new SongData("song_014", "My Generation", "The Who", 224),
        new SongData("song_015", "A Change Is Gonna Come", "Sam Cooke", 191)
    );

    private final KafkaProducer<String, String> producer;
    private final ObjectMapper objectMapper;
    private final Random random;

    public SongPlayProducer(String bootstrapServers) {
        Properties props = new Properties();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        props.put(ProducerConfig.ACKS_CONFIG, "all");
        props.put(ProducerConfig.RETRIES_CONFIG, 3);
        props.put(ProducerConfig.BATCH_SIZE_CONFIG, 16384);
        props.put(ProducerConfig.LINGER_MS_CONFIG, 1);
        props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, 33554432);

        this.producer = new KafkaProducer<>(props);
        this.objectMapper = new ObjectMapper();
        this.random = new Random();
    }

    public void generateSongPlay() {
        try {
            String userId = USER_IDS.get(random.nextInt(USER_IDS.size()));
            SongData songData = SONGS.get(random.nextInt(SONGS.size()));
            
            // Simulate bot behavior: some users play songs very frequently
            int playCount = 1;
            if (random.nextDouble() < 0.1) { // 10% chance of high play count (bot-like)
                playCount = random.nextInt(50) + 10;
            }
            
            SongPlay songPlay = new SongPlay(
                userId,
                songData.songId,
                songData.songTitle,
                songData.artist,
                songData.duration,
                playCount
            );

            String json = objectMapper.writeValueAsString(songPlay);
            String key = userId + "_" + songData.songId;
            
            ProducerRecord<String, String> record = new ProducerRecord<>(TOPIC, key, json);
            producer.send(record, (metadata, exception) -> {
                if (exception != null) {
                    logger.error("Error sending record: {}", exception.getMessage());
                } else {
                    logger.info("Sent song play: {} -> {}", key, songPlay.getSongTitle());
                }
            });

        } catch (Exception e) {
            logger.error("Error generating song play: {}", e.getMessage(), e);
        }
    }

    public void run(long intervalMs) {
        logger.info("Starting song play producer. Sending events every {} ms", intervalMs);
        
        try {
            while (true) {
                generateSongPlay();
                Thread.sleep(intervalMs);
            }
        } catch (InterruptedException e) {
            logger.info("Producer interrupted, shutting down...");
        } finally {
            producer.close();
        }
    }

    public static void main(String[] args) {
        String bootstrapServers = System.getenv("KAFKA_BROKERS");
        if (bootstrapServers == null) {
            bootstrapServers = "localhost:9092";
        }
        
        String intervalStr = System.getenv("PRODUCER_INTERVAL_MS");
        long intervalMs = intervalStr != null ? Long.parseLong(intervalStr) : 1000;
        
        SongPlayProducer producer = new SongPlayProducer(bootstrapServers);
        producer.run(intervalMs);
    }

    public static class SongData {
        final String songId;
        final String songTitle;
        final String artist;
        final int duration;

        SongData(String songId, String songTitle, String artist, int duration) {
            this.songId = songId;
            this.songTitle = songTitle;
            this.artist = artist;
            this.duration = duration;
        }
    }
} 