networks:
  app-network:
    driver: bridge

services:
  zookeeper:
    image: confluentinc/cp-zookeeper:7.5.0
    networks:
      - app-network
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2181:2181"

  kafka:
    image: confluentinc/cp-kafka:7.5.0
    depends_on: [zookeeper]
    networks:
      - app-network
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: 'zookeeper:2181'
      KAFKA_LISTENERS: PLAINTEXT://0.0.0.0:9092,PLAINTEXT_HOST://0.0.0.0:29092
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092,PLAINTEXT_HOST://localhost:29092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_INTER_<PERSON><PERSON>ER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
    ports:
      - "29092:29092"
      - "9092:9092"

  redpanda-console:
    container_name: redpanda-console
    image: docker.redpanda.com/redpandadata/console:v3.1.0
    depends_on:
      - kafka
    ports:
      - 8082:8080
      - 9644:9644
    entrypoint: /bin/sh
    command: -c 'echo "$$CONSOLE_CONFIG_FILE" > /tmp/config.yml; /app/console'
    environment:
      CONFIG_FILEPATH: /tmp/config.yml
      CONSOLE_CONFIG_FILE: |
        kafka:
          brokers: ["kafka:9092"]
        kafkaConnect:
          enabled: true
          clusters:
            - name: local-connect-cluster
              url: http://connect:8083
    networks:
      - app-network

  flink-jobmanager:
    image: flink:1.18.1
    command: jobmanager
    networks:
      - app-network
    ports:
      - "8081:8081"
    environment:
      - KAFKA_BROKERS=kafka:9092
      - |
        FLINK_PROPERTIES=
        jobmanager.rpc.address: flink-jobmanager
        jobmanager.rpc.port: 6123
        jobmanager.memory.process.size: 1600m
        taskmanager.memory.process.size: 1728m
        taskmanager.numberOfTaskSlots: 1
        parallelism.default: 1
        state.backend: hashmap
        # state.checkpoints.dir: file:///tmp/flink-checkpoints
        # state.savepoints.dir: file:///tmp/flink-savepoints
        # high-availability: zookeeper
        # high-availability.storageDir: file:///tmp/flink/ha/
        # high-availability.zookeeper.quorum: zookeeper:2181
        # high-availability.cluster-id: /cluster1
        rest.bind-port: 8081

  flink-taskmanager:
    image: flink:1.18.1
    command: taskmanager
    depends_on: [flink-jobmanager]
    networks:
      - app-network
    environment:
      - KAFKA_BROKERS=kafka:9092
      - |
        FLINK_PROPERTIES=
        jobmanager.rpc.address: flink-jobmanager
        jobmanager.rpc.port: 6123
        jobmanager.memory.process.size: 1600m
        taskmanager.memory.process.size: 1728m
        taskmanager.numberOfTaskSlots: 1
        parallelism.default: 1
        state.backend: hashmap
        # state.checkpoints.dir: file:///tmp/flink-checkpoints
        # state.savepoints.dir: file:///tmp/flink-savepoints
        # high-availability: zookeeper
        # high-availability.storageDir: file:///tmp/flink/ha/
        # high-availability.zookeeper.quorum: zookeeper:2181
        # high-availability.cluster-id: /cluster1

  flink-job:
    build: ./flink-job
    depends_on: [flink-jobmanager]
    networks:
      - app-network
    environment:
      - KAFKA_BROKERS=kafka:9092

  ws-backend:
    build: ./ws-backend
    depends_on: [kafka]
    networks:
      - app-network
    ports:
      - "8080:8080"
    environment:
      - KAFKA_BROKERS=kafka:9092
      - PORT=8080

  ui:
    build: ./ui
    depends_on: [ws-backend]
    networks:
      - app-network
    ports:
      - "3000:80"

  console-producer:
    build: ./console-producer
    depends_on: [kafka]
    networks:
      - app-network
    environment:
      - KAFKA_BROKERS=kafka:9092
      - PRODUCER_INTERVAL_MS=1000 