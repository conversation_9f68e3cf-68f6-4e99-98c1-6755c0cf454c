#!/bin/bash

# Deploy Flink Job Script
# This script helps deploy the Flink job to the running cluster

set -e

echo "🎯 Deploying Flink Job..."

# Check if Flink jobmanager is running
JOBMANAGER_CONTAINER_ID=$(docker-compose ps -q flink-jobmanager)
if [ -z "$JOBMANAGER_CONTAINER_ID" ]; then
    echo "❌ Flink jobmanager is not running. Please start it first:"
    echo "   docker-compose up -d flink-jobmanager"
    exit 1
fi

# Check if the JAR file exists
JAR_PATH="flink-job/target/flink-job-1.0.0.jar"
if [ ! -f "$JAR_PATH" ]; then
    echo "❌ Flink job JAR not found. Please build it first or copy from Docker container."
    exit 1
fi

echo "📦 Copying JAR to Flink jobmanager container..."

# Wait for Flink to be ready
echo "Waiting for Flink to be ready..."
sleep 10

# Copy the JAR file to the Flink jobmanager
echo "Copying JAR file to Flink jobmanager..."
docker cp flink-job/target/flink-job-1.0.0.jar ${JOBMANAGER_CONTAINER_ID}:/tmp/

# Submit the job in detached mode
echo "Submitting Flink job..."
docker exec -d ${JOBMANAGER_CONTAINER_ID} flink run \
    --class com.example.MusicAnalyticsJob \
    /tmp/flink-job-1.0.0.jar \
    --bootstrap.servers kafka:9092

# Wait a moment for the job to start
sleep 5

echo "Job submitted successfully!"
echo ""
echo "📊 You can monitor the job at: http://localhost:8081"
echo "🎵 The dashboard will start receiving data once the job is running" 