package com.example;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.Instant;

public class SongPlay {
    @JsonProperty("userId")
    private String userId;
    
    @JsonProperty("songId")
    private String songId;
    
    @JsonProperty("songTitle")
    private String songTitle;
    
    @JsonProperty("artist")
    private String artist;
    
    @JsonProperty("timestamp")
    private long timestamp;
    
    @JsonProperty("duration")
    private int duration; // in seconds
    
    @JsonProperty("playCount")
    private int playCount;

    public SongPlay() {}

    public SongPlay(String userId, String songId, String songTitle, String artist, int duration, int playCount) {
        this.userId = userId;
        this.songId = songId;
        this.songTitle = songTitle;
        this.artist = artist;
        this.timestamp = Instant.now().toEpochMilli();
        this.duration = duration;
        this.playCount = playCount;
    }

    // Getters and setters
    public String getUserId() { return userId; }
    public void setUserId(String userId) { this.userId = userId; }
    
    public String getSongId() { return songId; }
    public void setSongId(String songId) { this.songId = songId; }
    
    public String getSongTitle() { return songTitle; }
    public void setSongTitle(String songTitle) { this.songTitle = songTitle; }
    
    public String getArtist() { return artist; }
    public void setArtist(String artist) { this.artist = artist; }
    
    public long getTimestamp() { return timestamp; }
    public void setTimestamp(long timestamp) { this.timestamp = timestamp; }
    
    public int getDuration() { return duration; }
    public void setDuration(int duration) { this.duration = duration; }
    
    public int getPlayCount() { return playCount; }
    public void setPlayCount(int playCount) { this.playCount = playCount; }

    @Override
    public String toString() {
        return String.format("SongPlay{userId='%s', songId='%s', songTitle='%s', artist='%s', timestamp=%d, duration=%d, playCount=%d}",
                userId, songId, songTitle, artist, timestamp, duration, playCount);
    }
} 