import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, YA<PERSON>s, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

// Global WebSocket connection and data store
let globalWebSocket = null;
let globalSongMetrics = [];
let globalBotAlerts = [];
let globalTopSongs = [];
let globalSongPlays = [];
let globalIsConnected = false;
let globalListeners = new Set();
let globalLastRefreshTime = null;

function Dashboard() {
  const [isConnected, setIsConnected] = useState(globalIsConnected);
  const [songMetrics, setSongMetrics] = useState(globalSongMetrics);
  const [botAlerts, setBotAlerts] = useState(globalBotAlerts);
  const [topSongs, setTopSongs] = useState(globalTopSongs);
  const [songPlays, setSongPlays] = useState(globalSongPlays);
  const [lastRefreshTime, setLastRefreshTime] = useState(globalLastRefreshTime);
  const [currentTime, setCurrentTime] = useState(new Date()); // For "X seconds ago" counter
  const listenerRef = useRef(null);

  useEffect(() => {
    // Create a listener function for this component
    const listener = {
      updateConnection: (connected) => {
        setIsConnected(connected);
      },
      updateSongMetrics: (metrics) => {
        console.log('Received song metrics:', metrics.length, 'songs');
        setSongMetrics(metrics);
        setLastRefreshTime(new Date()); // Song metrics refresh every 1 minute (Flink window)
        console.log('Updated last refresh time for song metrics');
      },
      updateBotAlerts: (alerts) => {
        console.log('Received bot alerts:', alerts.length, 'alerts');
        setBotAlerts(alerts);
      },
      updateTopSongs: (songs) => {
        console.log('Received top songs:', songs.length, 'songs');
        setTopSongs(songs);
        // Top songs updates on every play event, so we don't update refresh time here
      },
      updateSongPlays: (plays) => {
        setSongPlays(plays);
      }
    };

    listenerRef.current = listener;
    globalListeners.add(listener);

    // Initialize WebSocket if not already connected
    if (!globalWebSocket) {
      initializeWebSocket();
    } else {
      // Update connection status
      setIsConnected(globalIsConnected);
    }

    // Initialize last refresh time if we already have song metrics data
    if (globalSongMetrics.length > 0 && !globalLastRefreshTime) {
      setLastRefreshTime(new Date());
    }

    return () => {
      globalListeners.delete(listener);
    };
  }, []);

  // Timer effect to update current time every second for "X seconds ago" counter
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const initializeWebSocket = () => {
    if (globalWebSocket && globalWebSocket.readyState === WebSocket.OPEN) {
      return; // Already connected
    }

    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}/ws`;

    globalWebSocket = new WebSocket(wsUrl);

    globalWebSocket.onopen = () => {
      console.log('WebSocket connected');
      globalIsConnected = true;
      // Notify all listeners
      globalListeners.forEach(listener => listener.updateConnection(true));
    };

    globalWebSocket.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        handleMessage(message);
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    };

    globalWebSocket.onclose = () => {
      console.log('WebSocket disconnected');
      globalIsConnected = false;
      // Notify all listeners
      globalListeners.forEach(listener => listener.updateConnection(false));
      
      // Attempt to reconnect after a delay
      setTimeout(() => {
        if (globalWebSocket.readyState === WebSocket.CLOSED) {
          initializeWebSocket();
        }
      }, 3000);
    };

    globalWebSocket.onerror = (error) => {
      console.error('WebSocket error:', error);
      globalIsConnected = false;
      // Notify all listeners
      globalListeners.forEach(listener => listener.updateConnection(false));
    };
  };

  const handleMessage = (message) => {
    switch (message.type) {
      case 'song_metrics':
        // Update global song metrics
        const existingIndex = globalSongMetrics.findIndex(song => song.songId === message.data.songId);
        if (existingIndex >= 0) {
          globalSongMetrics[existingIndex] = message.data;
        } else {
          globalSongMetrics.push(message.data);
        }
        // Sort by total plays
        globalSongMetrics.sort((a, b) => b.totalPlays - a.totalPlays);
        // Notify all listeners
        globalListeners.forEach(listener => listener.updateSongMetrics(globalSongMetrics));
        break;
      
      case 'bot_alert':
        // Update global bot alerts
        globalBotAlerts.unshift(message.data);
        // Keep only last 10 alerts
        globalBotAlerts = globalBotAlerts.slice(0, 10);
        // Notify all listeners
        globalListeners.forEach(listener => listener.updateBotAlerts(globalBotAlerts));
        break;
      
      case 'top_songs':
        // Update global top songs
        globalTopSongs = message.data.topSongs || [];
        // Notify all listeners
        globalListeners.forEach(listener => listener.updateTopSongs(globalTopSongs));
        break;
      
      case 'song_play':
        // Update global song plays
        globalSongPlays.unshift(message.data);
        // Keep only last 20 song plays
        globalSongPlays = globalSongPlays.slice(0, 20);
        // Notify all listeners
        globalListeners.forEach(listener => listener.updateSongPlays(globalSongPlays));
        break;
      
      case 'connection':
        console.log('Connection message:', message.message);
        break;
      
      default:
        console.log('Unknown message type:', message.type);
    }
  };

  const chartData = songMetrics.slice(0, 10).map(song => ({
    name: song.songTitle.length > 20 ? song.songTitle.substring(0, 20) + '...' : song.songTitle,
    plays: song.totalPlays,
    users: song.uniqueUsers,
    fullName: song.songTitle
  }));

  const formatTimeAgo = (timestamp) => {
    if (!timestamp) return 'Never';
    
    const diffMs = currentTime - timestamp;
    const diffSeconds = Math.floor(diffMs / 1000);
    const diffMinutes = Math.floor(diffSeconds / 60);
    
    if (diffMinutes === 0) {
      return `${diffSeconds} seconds ago`;
    } else if (diffMinutes === 1) {
      return '1 minute ago';
    } else {
      return `${diffMinutes} minutes ago`;
    }
  };

  const formatTimestamp = (timestamp) => {
    if (!timestamp) return 'Never';
    return timestamp.toLocaleTimeString();
  };

  return (
    <div className="container">
      <div className="header">
        <h1>🎵 Music Analytics Dashboard</h1>
        <p>Real-time streaming analytics powered by Apache Flink & Kafka</p>
      </div>

      <div className="connection-status">
        <span className={`status-indicator ${isConnected ? 'status-connected' : 'status-disconnected'}`}></span>
        {isConnected ? 'Connected to real-time data stream' : 'Disconnected - attempting to reconnect...'}
      </div>

      <div className="dashboard">
        <div className="card" style={{ maxHeight: '600px', width: 'calc(100% + 20px)' }}>
          <h2>📊 Top Songs (Last 1 Minute)</h2>
          <div style={{ height: '200px', marginBottom: '12px', flexShrink: 0 }}>
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip 
                  formatter={(value, name) => [value, name === 'plays' ? 'Total Plays' : 'Unique Users']}
                  labelFormatter={(label) => {
                    const song = chartData.find(d => d.name === label);
                    return song ? song.fullName : label;
                  }}
                />
                <Bar dataKey="plays" fill="#667eea" />
                <Bar dataKey="users" fill="#764ba2" />
              </BarChart>
            </ResponsiveContainer>
          </div>
          
          <div className="song-list">
            {songMetrics.slice(0, 5).map((song, index) => (
              <div key={song.songId} className="song-item">
                <div className="song-info">
                  <div className="song-title">{song.songTitle}</div>
                  <div className="song-artist">{song.artist}</div>
                </div>
                <div className="song-stats">
                  <div className="song-plays">{song.totalPlays} plays</div>
                  <div className="song-users">{song.uniqueUsers} users</div>
                </div>
              </div>
            ))}
          </div>
          
          {/* Last Refresh Timestamp for Top Songs (Last 1 Minute) */}
          <div style={{
            marginTop: '16px',
            padding: '8px 12px',
            background: 'rgba(102, 126, 234, 0.1)',
            borderRadius: '6px',
            fontSize: '12px',
            color: '#667eea',
            fontFamily: 'monospace',
            textAlign: 'center'
          }}>
            <div style={{ fontWeight: 'bold', marginBottom: '2px' }}>Last Updated</div>
            <div style={{ fontSize: '14px', marginBottom: '2px' }}>
              {lastRefreshTime ? formatTimestamp(lastRefreshTime) : 'Waiting for data...'}
            </div>
            <div style={{ fontSize: '11px', color: '#8b9dc3' }}>
              {lastRefreshTime ? formatTimeAgo(lastRefreshTime) : 'No data yet'}
            </div>
          </div>
        </div>

        <div className="card" style={{ maxHeight: '600px', width: 'calc(100% + 20px)' }}>
          <h2>🚨 Bot Detection Alerts</h2>
          <div className="alert-list">
            {botAlerts.length === 0 ? (
              <p style={{ color: '#6b7280', textAlign: 'center', marginTop: '20px' }}>
                No bot alerts detected yet
              </p>
            ) : (
              botAlerts.map((alert, index) => (
                <div key={index} className="alert-item">
                  <div className="alert-info">
                    <div className="alert-user">User: {alert.userId}</div>
                    <div className="alert-details">
                      {alert.totalPlays} plays in {Math.round(alert.timeWindow / 1000)} seconds
                    </div>
                  </div>
                  <div className="alert-threshold">
                    Threshold: {alert.threshold} plays
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        <div className="card" style={{ maxHeight: '600px', width: 'calc(100% + 20px)' }}>
          <h2>🏆 Top 5 Most Played Songs (All Time)</h2>
          <div className="top-songs-list">
            {topSongs.length === 0 ? (
              <p style={{ color: '#6b7280', textAlign: 'center', marginTop: '20px' }}>
                No song data available yet
              </p>
            ) : (
              topSongs.map((song, index) => (
                <div key={song.songId} className="top-song-item">
                  <div className="song-rank">#{index + 1}</div>
                  <div className="song-info">
                    <div className="song-title">{song.songTitle}</div>
                    <div className="song-artist">{song.artist}</div>
                  </div>
                  <div className="song-plays">
                    <div className="plays-count">{song.totalPlays}</div>
                    <div className="plays-label">plays</div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        <div className="card" style={{ maxHeight: '600px', width: 'calc(100% + 20px)' }}>
          <h2>🎵 Recent Song Plays</h2>
          <div className="song-plays-list">
            {songPlays.length === 0 ? (
              <p style={{ color: '#6b7280', textAlign: 'center', marginTop: '20px' }}>
                No songs played yet
              </p>
            ) : (
              songPlays.map((play, index) => (
                <div key={index} className="song-play-item">
                  <div className="play-info">
                    <div className="play-song">
                      <span className="play-title">{play.songTitle}</span>
                      <span className="play-artist">by {play.artist}</span>
                    </div>
                    <div className="play-user">User: {play.userId}</div>
                  </div>
                  <div className="play-time">
                    {new Date(play.timestamp).toLocaleTimeString()}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default Dashboard; 